robot:
  poses:
    - [[3], [4], [0], [0]]
    - [[8], [1], [1], [0]]
    - [[2], [6], [1], [0]]
    - [[7], [1], [0], [0]]
    - [[7], [6.5], [2], [0]]
    - [[9], [9], [3], [0]]
    - [[2], [9], [1], [0]]
    - [[3], [6], [3], [0]]
    - [[1], [7], [0], [0]]
    - [[5], [7], [3], [0]]
  goals:
    - [[8], [8], [0]]
    - [[2], [9], [0]]
    - [[7], [1], [0]]
    - [[7.2], [9], [0]]
    - [[1], [1], [0]]
    - [[5], [1], [0]]
    - [[7], [4], [0]]
    - [[9], [4], [0]]
    - [[1], [9], [0]]
    - [[5], [1], [0]]






      #robot_states = [np.array([[3],[4],[0],[0]]),
      #                    np.array([[8],[1],[1],[0]]),
      #                    np.array([[2],[6],[1],[0]]),
      #                    np.array([[7],[1],[0],[0]]),
      #                    np.array([[7],[6.5],[2],[0]]),
      #                    np.array([[9],[9],[3],[0]]),
      #                    np.array([[2], [9], [1], [0]]),
      #                    np.array([[3], [6], [3], [0]]),
      #                    np.array([[1], [7], [0], [0]]),
      #                    np.array([[5], [7], [3], [0]]),
      #                    ]
      #    robot_goals = [
      #        np.array([[8], [8], [0]]),
      #        np.array([[2], [9], [0]]),
      #        np.array([[7], [1], [0]]),
      #        np.array([[7.2], [9], [0]]),
      #        np.array([[1], [1], [0]]),
      #        np.array([[5], [1], [0]]),
      #        np.array([[7], [4], [0]]),
      #        np.array([[9], [4], [0]]),
      #        np.array([[1], [9], [0]]),
      #        np.array([[5], [1], [0]]),
      #    ]