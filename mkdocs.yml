extra:
  python_path:
  - ./robot_nav
nav:
- Home: index.md
- API Reference:
    IR-SIM: api/IR-SIM/ir-sim
    Models:
      - DDPG: api/models/DDPG.md
      - TD3: api/models/TD3.md
      - CNNTD3: api/models/cnntd3.md
      - RCPG: api/models/RCPG.md
      - HCM: api/models/HCM.md
      - PPO: api/models/PPO.md
      - SAC: api/models/SAC.md
    Training:
      - Train: api/Training/train.md
      - Train RNN: api/Training/trainrnn.md
    Testing:
      - Test: api/Testing/test.md
      - Test RNN: api/Testing/testrnn.md
    Utils:
      - Replay Buffer: api/Utils/replay_buffer.md
      - Utils: api/Utils/utils.md
plugins:
- search
- mkdocstrings
site_name: DRL-robot-navigation-IR-SIM
theme:
  name: material
