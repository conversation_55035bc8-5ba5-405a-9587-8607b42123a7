world:
  height: 10  # the height of the world
  width: 10   # the height of the world
  step_time: 0.3  # Hz calculate each step
  sample_time: 0.3  # Hz for render and data extraction
  collision_mode: 'reactive'

robot:
  - kinematics: {name: 'diff'}
    shape: {name: 'circle', radius: 0.2}
    vel_min: [ 0, -1.0 ]
    vel_max: [ 1.0, 1.0 ]
    state: [3, 4, 0, 0]
    goal: [9, 9, 0]
    arrive_mode: position
    goal_threshold: 0.3

    sensors:
      - type: 'lidar2d'
        range_min: 0
        range_max: 7
        angle_range: 3.14
        number: 180
        noise: True
        std: 0.08
        angle_std: 0.1
        offset: [ 0, 0, 0 ]
        alpha: 0.3

    plot:
      show_trajectory: True

obstacle:
  - shape: { name: 'circle', radius: 1.0 }  # radius
    state: [ 5, 5, 0 ]
  - shape: { name: 'circle', radius: 0.5 }  # radius
    state: [ 7, 8, 0 ]
  - shape: { name: 'circle', radius: 1.4 }  # radius
    state: [ 3, 1, 0 ]
  - shape: {name: 'rectangle', length: 1.0, width: 1.2}  # length, width
    state: [8, 5, 1]
  - shape: { name: 'rectangle', length: 0.5, width: 2.1 }  # length, width
    state: [ 1, 8, 1.3 ]
  - shape: { name: 'rectangle', length: 1.5, width: 0.7 }  # length, width
    state: [ 6, 2, 0.5 ]


  - shape: { name: 'linestring', vertices: [ [ 0, 0 ], [ 10, 0 ], [ 10, 10 ], [ 0, 10 ],[ 0, 0 ]  ] }  # vertices
    kinematics: {name: 'static'}
    state: [ 0, 0, 0 ]
