"""
DUNE (Deep Unfolded Neural Encoder) is the core class of the PAN class. It maps the point flow to the latent distance space: mu and lambda. 

Developed by <PERSON><PERSON><PERSON>
Copyright (c) 2025 <PERSON><PERSON><PERSON> <<EMAIL>>

NeuPAN planner is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

NeuPAN planner is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with NeuPAN planner. If not, see <https://www.gnu.org/licenses/>.
"""


import torch
from math import inf
from neupan.blocks import ObsPointNet, DUNETrain
from neupan.configuration import np_to_tensor, to_device
from neupan.util import time_it, file_check, repeat_mk_dirs
from typing import Optional
import sys
class DUNE(torch.nn.Module):

    def __init__(self, receding: int=10, checkpoint =None, robot=None, dune_max_num: int=100, train_kwargs: dict=dict()) -> None:
        super(DUNE, self).__init__()
  
        self.T = receding
        self.max_num = dune_max_num

        self.robot = robot

        self.G = np_to_tensor(robot.G)
        self.h = np_to_tensor(robot.h)
        self.edge_dim = self.G.shape[0]
        self.state_dim = self.G.shape[1]

        self.model = to_device(ObsPointNet(2, self.edge_dim))
        self.load_model(checkpoint, train_kwargs)

        self.obstacle_points = None
        self.min_distance = inf


        
    @time_it('- dune forward')
    def forward(self, point_flow: list[torch.Tensor], R_list: list[torch.Tensor], obs_points_list: list[torch.Tensor]=[]) -> tuple[list[torch.Tensor], list[torch.Tensor], list[torch.Tensor]]:
        '''
        map point flow to the latent distance features: lam, mu

        Args:
            point_flow: point flow under the robot coordinate, list of (state_dim, num_points); list length: T+1
            R_list: list of Rotation matrix, list of (2, 2), used to generate the lam from mu; list length: T
            obstacle_points: tensor of shape (2, num_points), global coordinate; 

        Returns: 
            lam_list: list of lam tensor, each element is a tensor of shape (state_dim, num_points); list length: T+1
            mu_list: list of mu tensor, each element is a tensor of shape (edge_number, num_points); list length: T+1
            sort_point_list: list of point tensor, each element is a tensor of shape (state_dim, num_points); list length: T+1; 
        '''
        # 检查输入是否为空
        if not point_flow or len(point_flow) == 0 or not obs_points_list or len(obs_points_list) == 0:
            self.min_distance = float('inf')
            return [], [], []
        
        # 检查第一个点流是否为空
        if point_flow[0].shape[1] == 0 or obs_points_list[0].shape[1] == 0:
            self.min_distance = float('inf')
            return [], [], []
        
        self.obstacle_points = obs_points_list[0]  # current obstacle points considered in the dune at time 0
        
        # 合并所有点流
        total_points = torch.cat(point_flow, dim=1)
        
        # 使用模型计算 mu
        with torch.no_grad():
            total_mu = self.model(total_points.T).T
        
        mu_list, lam_list, sort_point_list = [], [], []
        point_index = 0
        
        # 处理每个时间步
        for index in range(self.T+1):
            num_points = point_flow[index].shape[1]
            
            # 如果当前时间步没有点，添加空张量
            if num_points == 0:
                mu_list.append(torch.zeros((self.edge_dim, 0), device=self.G.device))
                lam_list.append(torch.zeros((self.state_dim, 0), device=self.G.device))
                sort_point_list.append(torch.zeros((2, 0), device=self.G.device))
                continue
            
            # 获取当前时间步的 mu
            mu = total_mu[:, point_index:point_index+num_points]
            point_index += num_points
            
            # 获取旋转矩阵和点流
            R = R_list[index]
            p0 = point_flow[index]
            
            # 计算 lambda
            lam = (- R @ self.G.T @ mu)
            
            # 处理单个点的情况
            if mu.ndim == 1:
                mu = mu.unsqueeze(1)
                lam = lam.unsqueeze(1)
            
            # 计算距离
            distance = self.cal_objective_distance(mu, p0)
            
            # 检查距离张量是否为空
            if distance.numel() == 0:
                mu_list.append(torch.zeros((self.edge_dim, 0), device=self.G.device))
                lam_list.append(torch.zeros((self.state_dim, 0), device=self.G.device))
                sort_point_list.append(torch.zeros((2, 0), device=self.G.device))
                continue
            
            # 更新最小距离
            if index == 0:
                # 安全地计算最小距离
                if distance.numel() > 0:
                    self.min_distance = torch.min(distance).item()
                else:
                    self.min_distance = float('inf')
            
            # 排序
            sort_indices = torch.argsort(distance)
            
            # 添加到列表
            mu_list.append(mu[:, sort_indices])
            lam_list.append(lam[:, sort_indices])
            sort_point_list.append(obs_points_list[index][:, sort_indices])
        
        return mu_list, lam_list, sort_point_list


    def cal_objective_distance(self, mu: torch.Tensor, p0: torch.Tensor) -> torch.Tensor:
        '''
        input: 
            mu: (edge_dim, num_points)
            p0: (state_dim, num_points)   
        output:
            distance:  mu.T (G @ p0 - h),  (num_points,)
        ''' 
        # 检查输入张量是否为空
        if mu.shape[1] == 0 or p0.shape[1] == 0:
            return torch.tensor([], device=mu.device)
        
        # 计算 G @ p0 - h
        Gp_h = self.G @ p0 - self.h.unsqueeze(1)
        
        # 转置并添加维度
        Gp_h_T = Gp_h.T.unsqueeze(2)
        mu_T = mu.T.unsqueeze(1)
        
        # 计算距离
        distance = torch.squeeze(torch.bmm(mu_T, Gp_h_T))
        
        # 确保结果是一维张量
        if distance.ndim == 0:
            distance = distance.unsqueeze(0)
        
        return distance
    


    def load_model(self, checkpoint: Optional[str]=None, train_kwargs: Optional[dict]=None):

        '''
        checkpoint: pth file path of the model
        '''

        try:
            if checkpoint is None:
                raise FileNotFoundError

            self.abs_checkpoint_path = file_check(checkpoint)
            self.model.load_state_dict(torch.load(self.abs_checkpoint_path, map_location=torch.device('cpu')))
            to_device(self.model)
            self.model.eval()

        except FileNotFoundError:

            if train_kwargs is None or len(train_kwargs) == 0:
                print('No train kwargs provided. Default value will be used.')
                train_kwargs = dict()
            
            direct_train = train_kwargs.get('direct_train', False)

            if direct_train:
                print('train or test the model directly.')
                return 

            if self.ask_to_train():
                self.train_dune(train_kwargs)

                if self.ask_to_continue():
                    self.model.load_state_dict(torch.load(self.full_model_name, map_location=torch.device('cpu')))
                    to_device(self.model)
                    self.model.eval()
                else:
                    print('You can set the new model path to the DUNE class to use the trained model.') 

            else:
                print('Can not find checkpoint. Please check the path or train first.')
                raise FileNotFoundError


    def train_dune(self, train_kwargs):

        model_name = train_kwargs.get("model_name", self.robot.name)

        checkpoint_path = sys.path[0] + '/model' + '/' + model_name
        checkpoint_path = repeat_mk_dirs(checkpoint_path)
        
        self.train_model = DUNETrain(self.model, self.G, self.h, checkpoint_path)
        self.full_model_name = self.train_model.start(**train_kwargs)
        print('Complete Training. The model is saved in ' + self.full_model_name)

    def ask_to_train(self):
        
        while True:
            choice = input("Do not find the DUNE model; Do you want to train the model now, input Y or N:").upper()
            if choice == 'Y':
                return True
            elif choice == 'N':
                print('Please set the your model path for the DUNE layer.')
                sys.exit()
            else:
                print("Wrong input, Please input Y or N.")


    def ask_to_continue(self):
        
        while True:
            choice = input("Do you want to continue the case running, input Y or N:").upper()
            if choice == 'Y':
                return True
            elif choice == 'N':
                print('exit the case running.')
                sys.exit()
            else:
                print("Wrong input, Please input Y or N.")


    @property
    def points(self):
        '''
        point considered in the dune layer
        '''

        return self.obstacle_points



