"""
Configuration utilities for DUNE module.

Extracted from NeuPAN's configuration.py
"""

import torch
import numpy as np

# Set device
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def np_to_tensor(np_array):
    """
    Convert numpy array to tensor
    
    Args:
        np_array: Numpy array
        
    Returns:
        torch.Tensor: Tensor on the configured device
    """
    if np_array is None:
        return None
        
    if isinstance(np_array, torch.Tensor):
        return np_array.to(device)
        
    return torch.tensor(np_array, dtype=torch.float32).to(device)

def tensor_to_np(tensor):
    """
    Convert tensor to numpy array
    
    Args:
        tensor: PyTorch tensor
        
    Returns:
        np.ndarray: Numpy array
    """