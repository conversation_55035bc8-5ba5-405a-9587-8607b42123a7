import torch
import numpy as np
import os
import sys
import cvxpy as cp
from typing import Optional, List, Tuple, Dict, Any
from torch.utils.data import DataLoader, TensorDataset
from filterpy.kalman import KalmanFilter

class ObsPointNet(torch.nn.Module):
    """
    从原始项目中提取的ObsPointNet模型
    """
    def __init__(self, input_dim: int, output_dim: int):
        super(ObsPointNet, self).__init__()
        # 定义网络结构
        self.net = torch.nn.Sequential(
            torch.nn.Linear(input_dim, 64),
            torch.nn.ReLU(),
            torch.nn.Linear(64, 128),
            torch.nn.ReLU(),
            torch.nn.Linear(128, 64),
            torch.nn.ReLU(),
            torch.nn.Linear(64, output_dim)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.net(x)

class DUNETrain:
    """
    从原始项目中提取的DUNETrain类，用于训练DUNE模型
    """
    def __init__(self, model, robot_G, robot_h, checkpoint_path):
        self.G = robot_G
        self.h = robot_h
        self.model = model
        self.checkpoint_path = checkpoint_path
        
        # 构建优化问题
        self.construct_problem()
        
        # 设置优化器和损失函数
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=1e-4, weight_decay=1e-4)
        self.loss_fn = torch.nn.MSELoss()
        
        # 记录训练损失
        self.loss_list = []
        
        # 设置设备
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = self.model.to(self.device)
    
    def construct_problem(self):
        """
        构建优化问题
        
        优化问题形式:
        max mu^T * (G * p - h)
        s.t. ||G^T * mu|| <= 1
            mu >= 0
        """
        self.mu = cp.Variable((self.G.shape[0], 1), nonneg=True)
        self.p = cp.Parameter((2, 1))  # 点坐标
        
        # 构建目标函数和约束
        cost = self.mu.T @ (self.G.cpu().numpy() @ self.p - self.h.cpu().numpy())
        constraints = [cp.norm(self.G.cpu().numpy().T @ self.mu) <= 1]
        
        # 创建问题
        self.prob = cp.Problem(cp.Maximize(cost), constraints)
    
    def generate_point_flow(self, nom_s: torch.Tensor, obs_points: torch.Tensor, point_velocities: Optional[torch.Tensor]=None):
        '''
        generate the point flow (robot coordinate), rotation matrix and obs points list in each receding step

        Args:
            nom_s: (3, receding+1)
            obs_points: (2, n)
            point_velocities: (2, n), x,y vel

        Returns:
            point_flow_list: list of (2, n); robot coordinate
            R_list: list of (2, 2); rotation matrix
            obs_points_list: list of (2, n); global coordinate
        '''
        
        if obs_points is None or obs_points.shape[1] == 0:
            return None, None, None
            
        if point_velocities is None:
            point_velocities = torch.zeros_like(obs_points)
        
        if obs_points.shape[1] > self.dune_max_num:
            self.print_once(f"down sample the obs points from {obs_points.shape[1]} to {self.dune_max_num}") 
            obs_points = downsample_decimation(obs_points, self.dune_max_num)
            point_velocities = downsample_decimation(point_velocities, self.dune_max_num)
        
        point_flow_list = []
        R_list = []
        obs_points_list = []
        
        for i in range(self.T+1):
            receding_obs_points = obs_points + i * (point_velocities * self.dt)
            obs_points_list.append(receding_obs_points) 
            p0, R = self.point_state_transform(nom_s[:, i], receding_obs_points)
            point_flow_list.append(p0)
            R_list.append(R)
        
        return point_flow_list, R_list, obs_points_list

    def point_state_transform(self, state: torch.Tensor, obs_points: torch.Tensor):

        '''
        transform the position of obstacle points to the robot coordinate system in each receding step
        
        input: 
            state: [x, y, theta] -- transition and rotation matrix
            obs_points: (2, n) -- point cloud

        output:
            p0: (2, n) point cloud in the robot coordinate system
            R: (2, 2) rotation matrix
        '''

        state = state.reshape((3, 1))
        trans = state[0:2]
        theta = state[2, 0]
        R = to_device(torch.tensor([[torch.cos(theta), -torch.sin(theta)], [torch.sin(theta), torch.cos(theta)]]))

        p0 = R.T @ (obs_points - trans)

        return p0, R
    
    def predict_with_kalman_filter(current_points, point_velocities, T, dt, measurement_noise=0.1, process_noise=0.01):
        """
        使用卡尔曼滤波预测障碍物点云
        
        Args:
            current_points: 当前障碍物点云，形状为(2, N)
            point_velocities: 障碍物点的速度，形状为(2, N)
            T: 预测时域长度
            dt: 时间步长
            measurement_noise: 测量噪声
            process_noise: 过程噪声
            
        Returns:
            predicted_points_list: 预测的障碍物点云列表
        """
        
        # 获取点的数量
        _, N = current_points.shape
        
        # 初始化预测点云列表
        predicted_points_list = [current_points]
        
        # 为每个点创建卡尔曼滤波器
        filters = []
        for i in range(N):
            # 创建滤波器：状态=[x, y, vx, vy]
            kf = KalmanFilter(dim_x=4, dim_z=2)
            
            # 设置转移矩阵
            kf.F = np.array([
                [1, 0, dt, 0],
                [0, 1, 0, dt],
                [0, 0, 1, 0],
                [0, 0, 0, 1]
            ])
            
            # 设置测量矩阵
            kf.H = np.array([
                [1, 0, 0, 0],
                [0, 1, 0, 0]
            ])
            
            # 设置过程噪声协方差
            kf.Q = process_noise * np.eye(4)
            
            # 设置测量噪声协方差
            kf.R = measurement_noise * np.eye(2)
            
            # 初始化状态
            kf.x = np.array([
                current_points[0, i],
                current_points[1, i],
                point_velocities[0, i],
                point_velocities[1, i]
            ])
            
            filters.append(kf)
        
        # 预测未来T个时间步
        for t in range(1, T+1):
            # 存储当前时间步的预测点
            current_predicted = np.zeros_like(current_points)
            
            # 对每个点进行预测
            for i in range(N):
                # 预测下一个状态
                filters[i].predict()
                
                # 提取位置
                current_predicted[0, i] = filters[i].x[0]
                current_predicted[1, i] = filters[i].x[1]
            
            # 添加到预测列表
            predicted_points_list.append(current_predicted)
        
        return predicted_points_list
    
    def generate_data_set(self, data_size=10000, data_range=[-50, -50, 50, 50]):
        """
        生成训练数据集
        
        Args:
            data_size: 数据集大小
            data_range: 数据范围 [x_min, y_min, x_max, y_max]
            
        Returns:
            data_list: 包含(点, mu, 距离)的数据列表
        """
        print(f"Generating dataset with {data_size} points...")
        
        # 随机生成点
        x_min, y_min, x_max, y_max = data_range
        x_values = np.random.uniform(x_min, x_max, data_size)
        y_values = np.random.uniform(y_min, y_max, data_size)
        
        # 组合成点
        points = np.vstack((x_values, y_values))
        
        # 处理每个点
        data_list = []
        for i in range(data_size):
            p = points[:, i:i+1]
            p_tensor, mu_tensor, distance = self.process_data(p)
            data_list.append((p_tensor, mu_tensor, distance))
        
        print("Dataset generation completed.")
        return data_list
    
    def process_data(self, p):
        """
        处理数据点
        
        Args:
            p: 形状为(2, 1)的点
            
        Returns:
            p_tensor: 点的张量表示
            mu_tensor: mu的张量表示
            distance: 距离值
        """
        # 求解优化问题
        distance_value, mu_value = self.prob_solve(p)
        
        # 转换为张量
        p_tensor = np_to_tensor(p)
        mu_tensor = np_to_tensor(mu_value)
        distance = np_to_tensor(distance_value)
        
        return p_tensor, mu_tensor, distance
    
    def prob_solve(self, p_value):
        """
        求解优化问题
        
        Args:
            p_value: 点坐标
            
        Returns:
            distance_value: 距离值
            mu_value: mu值
        """
        # 设置参数值
        self.p.value = p_value
        
        # 求解问题
        self.prob.solve(solver=cp.ECOS)
        
        # 获取结果
        mu_value = self.mu.value
        
        # 计算距离
        distance_value = mu_value.T @ (self.G.cpu().numpy() @ p_value - self.h.cpu().numpy())
        
        return distance_value.item(), mu_value
    
    def start(self, data_size=100000, data_range=[-25, -25, 25, 25], 
              batch_size=256, epoch=5000, valid_freq=100, save_freq=500,
              lr=5e-5, lr_decay=0.5, decay_freq=1500, save_loss=False, **kwargs):
        """
        开始训练过程
        
        Args:
            data_size: 数据集大小
            data_range: 数据范围
            batch_size: 批次大小
            epoch: 训练轮数
            valid_freq: 验证频率
            save_freq: 保存频率
            lr: 学习率
            lr_decay: 学习率衰减
            decay_freq: 衰减频率
            save_loss: 是否保存损失
            
        Returns:
            model_path: 保存的模型路径
        """
        print(f"Starting training with {epoch} epochs...")
        
        # 生成数据集
        data_list = self.generate_data_set(data_size, data_range)
        
        # 分割训练集和验证集
        train_size = int(0.9 * len(data_list))
        train_data = data_list[:train_size]
        valid_data = data_list[train_size:]
        
        # 创建数据加载器
        train_points = torch.stack([item[0] for item in train_data]).squeeze(2)
        train_mus = torch.stack([item[1] for item in train_data]).squeeze(2)
        
        valid_points = torch.stack([item[0] for item in valid_data]).squeeze(2)
        valid_mus = torch.stack([item[1] for item in valid_data]).squeeze(2)
        
        train_dataset = TensorDataset(train_points.T, train_mus.T)
        valid_dataset = TensorDataset(valid_points.T, valid_mus.T)
        
        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        valid_dataloader = DataLoader(valid_dataset, batch_size=batch_size)
        
        # 设置优化器和学习率调度器
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.StepLR(self.optimizer, step_size=decay_freq, gamma=lr_decay)
        
        # 训练循环
        best_valid_loss = float('inf')
        model_name = kwargs.get("model_name", "dune_model")
        
        for e in range(epoch):
            # 训练一个epoch
            train_loss = self.train_one_epoch(train_dataloader)
            
            # 更新学习率
            scheduler.step()
            
            # 验证
            if (e + 1) % valid_freq == 0:
                with torch.no_grad():
                    valid_loss = self.train_one_epoch(valid_dataloader, validate=True)
                print(f"Epoch {e+1}/{epoch}, Train Loss: {train_loss:.6f}, Valid Loss: {valid_loss:.6f}")
                
                # 保存最佳模型
                if valid_loss < best_valid_loss:
                    best_valid_loss = valid_loss
                    best_model_path = os.path.join(self.checkpoint_path, f"{model_name}_best.pth")
                    torch.save(self.model.state_dict(), best_model_path)
                    print(f"Best model saved to {best_model_path}")
            
            # 定期保存模型
            if (e + 1) % save_freq == 0:
                model_path = os.path.join(self.checkpoint_path, f"{model_name}_{e+1}.pth")
                torch.save(self.model.state_dict(), model_path)
                print(f"Model checkpoint saved to {model_path}")
            
            # 记录损失
            self.loss_list.append(train_loss)
        
        # 保存最终模型
        final_model_path = os.path.join(self.checkpoint_path, f"{model_name}_final.pth")
        torch.save(self.model.state_dict(), final_model_path)
        print(f"Final model saved to {final_model_path}")
        
        # 保存损失记录
        if save_loss:
            loss_path = os.path.join(self.checkpoint_path, f"{model_name}_loss.npy")
            np.save(loss_path, np.array(self.loss_list))
            print(f"Loss history saved to {loss_path}")
        
        return final_model_path
    
    def train_one_epoch(self, dataloader, validate=False):
        """
        训练一个epoch
        
        Args:
            dataloader: 数据加载器
            validate: 是否为验证模式
            
        Returns:
            avg_loss: 平均损失
        """
        total_loss = 0.0
        total_batches = 0
        
        # 设置模型模式
        if validate:
            self.model.eval()
        else:
            self.model.train()
        
        for points, mus in dataloader:
            # 移动数据到设备
            points = points.to(self.device)
            mus = mus.to(self.device)
            
            # 前向传播
            pred_mus = self.model(points)
            
            # 计算损失
            loss = self.loss_fn(pred_mus, mus)
            
            # 反向传播和优化
            if not validate:
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()
            
            # 累加损失
            total_loss += loss.item()
            total_batches += 1
        
        # 计算平均损失
        avg_loss = total_loss / total_batches
        return avg_loss
    
    def test(self, model_path, train_dict_kwargs, data_size_list=0, **kwargs):
        """
        测试模型
        
        Args:
            model_path: 模型路径
            train_dict_kwargs: 训练参数
            data_size_list: 测试数据大小
            
        Returns:
            test_loss: 测试损失
        """
        # 加载模型
        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.model.eval()
        
        # 生成测试数据
        if isinstance(data_size_list, int):
            data_size_list = [data_size_list]
        
        data_range = train_dict_kwargs.get("data_range", [-25, -25, 25, 25])
        
        total_loss = 0.0
        total_samples = 0
        
        for data_size in data_size_list:
            if data_size <= 0:
                continue
                
            # 生成测试数据
            test_data = self.generate_data_set(data_size, data_range)
            
            # 准备测试数据
            test_points = torch.stack([item[0] for item in test_data]).squeeze(2)
            test_mus = torch.stack([item[1] for item in test_data]).squeeze(2)
            
            test_dataset = TensorDataset(test_points.T, test_mus.T)
            test_dataloader = DataLoader(test_dataset, batch_size=256)
            
            # 测试
            with torch.no_grad():
                for points, mus in test_dataloader:
                    points = points.to(self.device)
                    mus = mus.to(self.device)
                    
                    pred_mus = self.model(points)
                    loss = self.loss_fn(pred_mus, mus)
                    
                    total_loss += loss.item() * points.size(0)
                    total_samples += points.size(0)
        
        # 计算平均损失
        if total_samples > 0:
            avg_loss = total_loss / total_samples
            print(f"Test Loss: {avg_loss:.6f}")
            return avg_loss
        else:
            print("No test samples.")
            return None

class StandaloneDUNE(torch.nn.Module):
    """
    独立的DUNE模块，可以单独使用，不依赖于原始项目的其他部分
    """
    def __init__(self, 
                 G: torch.Tensor,
                 h: torch.Tensor,
                 receding: int = 10, 
                 checkpoint: Optional[str] = None, 
                 dune_max_num: int = 100,
                 train_kwargs: Dict[str, Any] = None) -> None:
        """
        初始化独立DUNE模块
        
        Args:
            G: 机器人约束矩阵，形状为(edge_dim, state_dim)
            h: 机器人约束向量，形状为(edge_dim, 1)
            receding: 预测步长
            checkpoint: 模型检查点路径
            dune_max_num: DUNE最大点数
            train_kwargs: 训练参数
        """
        super(StandaloneDUNE, self).__init__()
        
        self.T = receding
        self.max_num = dune_max_num
        
        self.G = G
        self.h = h
        self.edge_dim = self.G.shape[0]
        self.state_dim = self.G.shape[1]
        
        # 初始化模型
        self.model = ObsPointNet(2, self.edge_dim)
        self.load_model(checkpoint, train_kwargs or {})
        
        self.obstacle_points = None
        self.min_distance = float('inf')
        
        # 设置设备
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = self.model.to(self.device)
        self.G = self.G.to(self.device)
        self.h = self.h.to(self.device)
    
    def forward(self, 
                point_flow: List[torch.Tensor], 
                R_list: List[torch.Tensor], 
                obs_points_list: List[torch.Tensor] = None) -> Tuple[List[torch.Tensor], List[torch.Tensor], List[torch.Tensor]]:
        """
        前向传播函数
        
        Args:
            point_flow: 机器人坐标系下的点流，列表长度为T+1，每个元素形状为(state_dim, num_points)
            R_list: 旋转矩阵列表，列表长度为T，每个元素形状为(2, 2)
            obs_points_list: 障碍物点列表，可选
            
        Returns:
            lam_list: lam张量列表
            mu_list: mu张量列表
            sort_point_list: 排序后的点张量列表
        """
        mu_list, lam_list, sort_point_list = [], [], []
        
        # 重置最小距离
        self.min_distance = float('inf')
        
        # 处理障碍物点
        if obs_points_list and len(obs_points_list) > 0:
            self.obstacle_points = obs_points_list[0]  # 当前时间步的障碍物点
        
        # 将所有点合并为一个大张量
        total_points = torch.hstack(point_flow).to(self.device)
        
        # 映射点流到潜在距离特征mu
        with torch.no_grad():
            total_mu = self.model(total_points.T).T
        
        # 处理每个时间步
        start_idx = 0
        for t, p in enumerate(point_flow):
            p = p.to(self.device)
            num_points = p.shape[1]
            end_idx = start_idx + num_points
            
            mu = total_mu[:, start_idx:end_idx]
            
            # 计算lam
            if t < len(R_list):
                R = R_list[t].to(self.device)
                lam = R.T @ self.G.T @ mu
            else:
                # 对于最后一个时间步，使用最后一个R
                R = R_list[-1].to(self.device)
                lam = R.T @ self.G.T @ mu
            
            # 计算目标距离
            distance = self.cal_objective_distance(mu, p)
            
            # 更新最小距离
            min_dist = torch.min(distance).item()
            if min_dist < self.min_distance:
                self.min_distance = min_dist
            
            # 添加到结果列表
            mu_list.append(mu)
            lam_list.append(lam)
            sort_point_list.append(p)
            
            start_idx = end_idx
        
        return lam_list, mu_list, sort_point_list
    
    def cal_objective_distance(self, mu: torch.Tensor, p0: torch.Tensor) -> torch.Tensor:
        """
        计算目标距离
        
        Args:
            mu: 形状为(edge_dim, num_points)的张量
            p0: 形状为(state_dim, num_points)的张量
            
        Returns:
            distance: 形状为(num_points,)的张量
        """
        temp = (self.G @ p0 - self.h).T.unsqueeze(2)
        muT = mu.T.unsqueeze(1)
        
        distance = torch.squeeze(torch.bmm(muT, temp))
        
        if distance.ndim == 0:
            distance = distance.unsqueeze(0)
        
        return distance
    
    def load_model(self, checkpoint: Optional[str] = None, train_kwargs: Dict[str, Any] = None):
        """
        加载模型
        
        Args:
            checkpoint: 模型检查点路径
            train_kwargs: 训练参数
        """
        try:
            if checkpoint is None:
                raise FileNotFoundError
            
            self.model.load_state_dict(torch.load(checkpoint, map_location=self.device))
            self.model.eval()
            print(f"Model loaded from {checkpoint}")
            
        except FileNotFoundError:
            if train_kwargs is None or len(train_kwargs) == 0:
                print('No train kwargs provided. Default values will be used.')
                train_kwargs = {}
            
            direct_train = train_kwargs.get('direct_train', False)
            
            if direct_train:
                print('Train or test the model directly.')
                return
            
            print("Model checkpoint not found. Please provide a valid checkpoint or train the model first.")
            raise FileNotFoundError
    
    def train_dune(self, train_kwargs: Dict[str, Any]):
        """
        训练DUNE模型
        
        Args:
            train_kwargs: 训练参数
        """
        model_name = train_kwargs.get("model_name", "dune_model")
        
        # 创建检查点目录
        checkpoint_path = os.path.join(os.getcwd(), 'model', model_name)
        os.makedirs(checkpoint_path, exist_ok=True)
        
        # 初始化训练器
        trainer = DUNETrain(self.model, self.G, self.h, checkpoint_path)
        
        # 开始训练
        full_model_name = trainer.start(**train_kwargs)
        print('Complete Training. The model is saved in ' + full_model_name)
        
        # 加载训练好的模型
        self.model.load_state_dict(torch.load(full_model_name, map_location=self.device))
        self.model.eval()
        
        return full_model_name
    
    def process_lidar_data(self, lidar_points, robot_state=None):
        """
        处理激光雷达数据
        
        Args:
            lidar_points: 激光雷达点云数据，形状为(2, num_points)
            robot_state: 机器人当前状态，用于坐标变换
            
        Returns:
            features: 处理后的特征
        """
        # 转换为张量
        points_tensor = np_to_tensor(lidar_points).to(self.device)
        
        # 如果需要坐标变换
        if robot_state is not None:
            # 实现坐标变换逻辑
            pass
        
        # 构建DUNE输入
        point_flow = [points_tensor]
        R_list = [torch.eye(2, device=self.device)]
        obs_points_list = [points_tensor]
        
        # 使用DUNE处理数据
        lam_list, mu_list, _ = self(point_flow, R_list, obs_points_list)
        
        # 构建特征
        features = {
            'mu': mu_list[0],
            'lam': lam_list[0],
            'min_distance': self.min_distance
        }
        
        return features
    
    def export_model(self, example_input, output_path="dune_model.pt"):
        """
        导出模型为TorchScript格式
        
        Args:
            example_input: 示例输入，形状为(batch_size, 2)
            output_path: 输出路径
        """
        self.model.eval()
        
        # 使用torch.jit.script导出模型
        scripted_model = torch.jit.script(self.model)
        
        # 保存模型
        scripted_model.save(output_path)
        print(f"Model exported to {output_path}")
        
        return output_path

# 辅助函数
def to_device(model):
    """将模型移动到可用设备上"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    return model.to(device)

def np_to_tensor(array):
    """将numpy数组转换为张量"""
    if isinstance(array, torch.Tensor):
        return array
    return torch.tensor(array, dtype=torch.float32)

# 使用示例
def main():
    # 创建约束矩阵和向量
    G = np_to_tensor(np.array([
        [1, 0],
        [0, 1],
        [-1, 0],
        [0, -1]
    ]))
    
    h = np_to_tensor(np.array([[1], [1], [1], [1]]))
    
    # 初始化DUNE模块
    dune = StandaloneDUNE(G=G, h=h)
    
    # 创建示例输入
    point_flow = [torch.randn(2, 10) for _ in range(5)]
    R_list = [torch.eye(2) for _ in range(4)]
    obs_points = torch.randn(2, 20)
    
    # 处理数据
    lam_list, mu_list, _ = dune(point_flow, R_list, [obs_points])
    
    print(f"Minimum distance: {dune.min_distance}")
    print(f"mu shape: {mu_list[0].shape}")
    print(f"lam shape: {lam_list[0].shape}")

if __name__ == "__main__":
    main()

