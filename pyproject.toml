[project]
name = "robot-nav"
version = "0.1.0"
description = ""
authors = [
    {name = "reinis",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "ir-sim[all] (>=2.3.0,<3.0.0)",
    "tensorboard (>=2.18.0,<3.0.0)",
    "tqdm (>=4.67.1,<5.0.0)",
    "pytest (>=8.3.4,<9.0.0)",
    "mkdocs (>=1.6.1,<2.0.0)",
    "mkdocstrings[python] (>=0.29.1,<0.30.0)",
    "mkdocs-material (>=9.6.11,<10.0.0)",
    "scipy (>=1.15.2,<2.0.0)"
]

[tool.pytest.ini_options]
testpaths = ["tests"]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
